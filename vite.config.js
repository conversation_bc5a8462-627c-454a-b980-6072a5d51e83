import {defineConfig} from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig(() => {
  return {
    plugins: [vue()], resolve: {
      alias: {
        '@': '/src',
      },
    }, // 路径别名配置
    server: {
      host: '0.0.0.0', // 服务器地址
      hmr: true, // 启用热更新
      proxy: {
        '/api': {
          target: 'http://gateway.xyd-dev.zszc.jianshicha.cn/', // 目标服务器地址
          ws: true, // 是否启用 WebSocket
          changeOrigin: true, // 是否修改请求头中的 Origin 字段
          rewrite: (path) => path.replace(/^\/api/, ''), configure: (proxy, options) => {
            console.log('🚀 代理配置已加载');
            // 监听代理成功
            proxy.on('proxyRes', async (proxyRes, req, res) => {
              console.log(`✅ 真实后端: ${req.method} ${req.url} - ${proxyRes.statusCode}`);
              // 如果真实接口返回404，拦截并替换为Mock响应
              if (proxyRes.statusCode === 500) {
                // 阻止原始响应传递给前端
                // res.removeAllListeners();
                proxyRes.destroy();
                // 完全模拟真实接口的响应格式
                res.writeHead(200, {
                  'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*',
                });
                const axios = (await import('axios')).default;
                const mockResponse = await axios({
                  ...req, baseURL: 'http://127.0.0.1:4523/m1/6694373-6404027-f27ece95'
                });
                res.end(JSON.stringify(mockResponse.data));
                console.log(`✨ Mock响应已无缝替换`);
              }
            });
          },
        }
      },
    },
  }
})
