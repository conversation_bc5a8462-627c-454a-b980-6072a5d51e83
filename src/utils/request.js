import axios from 'axios';


/**
 * 创建并配置一个 Axios 实例对象
 */
const service = axios.create({
    baseURL: '/api',
    timeout: 50000, // 全局超时时间
});

service.interceptors.request.use(
    (config) => {
        return config;
    },
    (error) => {
        // 对请求错误进行处理
        return Promise.reject(error);
    }
);

/**
 * 添加 Axios 的响应拦截器，用于全局响应结果处理
 */
service.interceptors.response.use((response) => {
    return response.data;
}, (error) => {
    return Promise.reject(error.response.data);
});

// 导出 axios 实例
export default service;
